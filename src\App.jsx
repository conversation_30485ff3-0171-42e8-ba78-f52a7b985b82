import React, { useState } from 'react';

// Premium Synth Design System Colors
const synthColors = {
  // Rich neutral base with warm undertones
  base: '#F5F4F2',
  baseSecondary: '#EFEDE9',
  light: '#FEFDFB',
  lightSecondary: '#F9F8F6',

  // Sophisticated shadows
  dark: '#D4D1CC',
  darkSecondary: '#C8C4BD',
  shadow: '#BEB9B0',

  // Premium accent colors - Deep purple with gold undertones
  accent: '#6B46C1',
  accentLight: '#8B5CF6',
  accentDark: '#553C9A',

  // Secondary accent - Warm gold
  gold: '#D97706',
  goldLight: '#F59E0B',
  goldDark: '#B45309',

  // Rich text colors
  text: '#1F2937',
  textSecondary: '#374151',
  textTertiary: '#6B7280',
  textLight: '#9CA3AF',

  // Premium backgrounds
  bgPrimary: '#FAFAF9',
  bgSecondary: '#F7F6F4',
  bgTertiary: '#F3F2EF',

  // Status colors with premium feel
  success: '#059669',
  warning: '#D97706',
  error: '#DC2626',

  // Gradient stops
  gradientStart: '#6B46C1',
  gradientEnd: '#8B5CF6',
  gradientGold: '#F59E0B'
};

// Synth Button Component with different states
const SynthButton = ({ children, variant = 'elevated', size = 'md', onClick, style = {}, ...props }) => {
  const [isPressed, setIsPressed] = useState(false);

  const baseStyles = {
    border: 'none',
    cursor: 'pointer',
    fontWeight: '500',
    transition: 'all 0.15s ease',
    fontFamily: 'inherit',
    outline: 'none'
  };

  const sizeStyles = {
    sm: { padding: '0.5rem 1rem', fontSize: '14px', borderRadius: '8px' },
    md: { padding: '0.75rem 1.5rem', fontSize: '16px', borderRadius: '12px' },
    lg: { padding: '1rem 2rem', fontSize: '18px', borderRadius: '16px' }
  };

  const variantStyles = {
    elevated: {
      background: `linear-gradient(145deg, ${synthColors.light}, ${synthColors.base})`,
      color: synthColors.text,
      boxShadow: isPressed
        ? `inset 6px 6px 12px ${synthColors.dark}, inset -6px -6px 12px ${synthColors.light}`
        : `8px 8px 16px ${synthColors.dark}, -8px -8px 16px ${synthColors.light}, inset 0 1px 0 ${synthColors.lightSecondary}`,
      transform: isPressed ? 'scale(0.98)' : 'scale(1)',
      border: `1px solid ${synthColors.lightSecondary}`
    },
    flat: {
      background: `linear-gradient(145deg, ${synthColors.baseSecondary}, ${synthColors.base})`,
      color: synthColors.text,
      boxShadow: isPressed
        ? `inset 3px 3px 6px ${synthColors.dark}, inset -3px -3px 6px ${synthColors.light}`
        : `4px 4px 8px ${synthColors.dark}, -4px -4px 8px ${synthColors.light}`,
      transform: isPressed ? 'scale(0.99)' : 'scale(1)',
      border: `1px solid ${synthColors.base}`
    },
    accent: {
      background: `linear-gradient(135deg, ${synthColors.accent}, ${synthColors.accentDark})`,
      color: 'white',
      boxShadow: isPressed
        ? `inset 4px 4px 8px rgba(85, 60, 154, 0.4), inset -4px -4px 8px rgba(139, 92, 246, 0.4)`
        : `8px 8px 16px rgba(107, 70, 193, 0.3), -8px -8px 16px ${synthColors.light}, inset 0 1px 0 rgba(255, 255, 255, 0.2)`,
      transform: isPressed ? 'scale(0.98)' : 'scale(1)',
      border: `1px solid ${synthColors.accentLight}`
    },
    gold: {
      background: `linear-gradient(135deg, ${synthColors.gold}, ${synthColors.goldDark})`,
      color: 'white',
      boxShadow: isPressed
        ? `inset 4px 4px 8px rgba(180, 83, 9, 0.4), inset -4px -4px 8px rgba(245, 158, 11, 0.4)`
        : `8px 8px 16px rgba(217, 119, 6, 0.3), -8px -8px 16px ${synthColors.light}, inset 0 1px 0 rgba(255, 255, 255, 0.2)`,
      transform: isPressed ? 'scale(0.98)' : 'scale(1)',
      border: `1px solid ${synthColors.goldLight}`
    },
    ghost: {
      background: 'transparent',
      color: synthColors.text,
      boxShadow: isPressed
        ? `inset 2px 2px 4px ${synthColors.dark}, inset -2px -2px 4px ${synthColors.light}`
        : 'none',
      transform: isPressed ? 'scale(0.98)' : 'scale(1)',
      border: `2px solid ${synthColors.dark}`,
      backdropFilter: 'blur(10px)'
    }
  };

  return (
    <button
      style={{
        ...baseStyles,
        ...sizeStyles[size],
        ...variantStyles[variant],
        ...style
      }}
      onMouseDown={() => setIsPressed(true)}
      onMouseUp={() => setIsPressed(false)}
      onMouseLeave={() => setIsPressed(false)}
      onClick={onClick}
      {...props}
    >
      {children}
    </button>
  );
};

// Premium Synth Card Component
const SynthCard = ({ children, variant = 'elevated', style = {}, ...props }) => {
  const [isHovered, setIsHovered] = useState(false);

  const baseStyles = {
    borderRadius: '24px',
    padding: '2rem',
    transition: 'all 0.4s cubic-bezier(0.4, 0, 0.2, 1)',
    position: 'relative',
    overflow: 'hidden',
    backdropFilter: 'blur(10px)'
  };

  const variantStyles = {
    elevated: {
      background: `linear-gradient(145deg, ${synthColors.light}, ${synthColors.base})`,
      boxShadow: isHovered
        ? `12px 12px 24px ${synthColors.dark}, -12px -12px 24px ${synthColors.light}, inset 0 1px 0 ${synthColors.lightSecondary}`
        : `10px 10px 20px ${synthColors.dark}, -10px -10px 20px ${synthColors.light}, inset 0 1px 0 ${synthColors.lightSecondary}`,
      transform: isHovered ? 'translateY(-6px) scale(1.02)' : 'translateY(0) scale(1)',
      border: `1px solid ${synthColors.lightSecondary}`
    },
    flat: {
      background: `linear-gradient(145deg, ${synthColors.baseSecondary}, ${synthColors.base})`,
      boxShadow: isHovered
        ? `6px 6px 12px ${synthColors.dark}, -6px -6px 12px ${synthColors.light}`
        : `5px 5px 10px ${synthColors.dark}, -5px -5px 10px ${synthColors.light}`,
      transform: isHovered ? 'translateY(-3px)' : 'translateY(0)',
      border: `1px solid ${synthColors.base}`
    },
    inset: {
      background: `linear-gradient(145deg, ${synthColors.base}, ${synthColors.baseSecondary})`,
      boxShadow: `inset 6px 6px 12px ${synthColors.dark}, inset -6px -6px 12px ${synthColors.light}`,
      border: `1px solid ${synthColors.darkSecondary}`
    },
    premium: {
      background: `linear-gradient(145deg, ${synthColors.light}, ${synthColors.baseSecondary})`,
      boxShadow: isHovered
        ? `15px 15px 30px ${synthColors.shadow}, -15px -15px 30px ${synthColors.light}, inset 0 1px 0 rgba(255, 255, 255, 0.8)`
        : `12px 12px 24px ${synthColors.shadow}, -12px -12px 24px ${synthColors.light}, inset 0 1px 0 rgba(255, 255, 255, 0.6)`,
      transform: isHovered ? 'translateY(-8px) scale(1.03)' : 'translateY(0) scale(1)',
      border: `2px solid ${synthColors.lightSecondary}`,
      backdropFilter: 'blur(20px)'
    }
  };

  return (
    <div
      style={{
        ...baseStyles,
        ...variantStyles[variant],
        ...style
      }}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
      {...props}
    >
      {/* Premium highlight effects */}
      <div style={{
        position: 'absolute',
        top: 0,
        left: '15%',
        right: '15%',
        height: '2px',
        background: `linear-gradient(90deg, transparent, ${synthColors.lightSecondary}, ${synthColors.light}, ${synthColors.lightSecondary}, transparent)`,
        opacity: 0.9,
        borderRadius: '0 0 2px 2px'
      }} />

      {/* Subtle corner accents */}
      <div style={{
        position: 'absolute',
        top: '1rem',
        right: '1rem',
        width: '4px',
        height: '4px',
        background: `linear-gradient(135deg, ${synthColors.accent}, ${synthColors.accentLight})`,
        borderRadius: '50%',
        opacity: 0.3
      }} />

      {children}
    </div>
  );
};

// Premium Navigation Component
const Navigation = () => (
  <nav style={{
    position: 'fixed',
    top: 0,
    left: 0,
    right: 0,
    background: `rgba(250, 250, 249, 0.95)`,
    backdropFilter: 'blur(30px)',
    borderBottom: `2px solid ${synthColors.lightSecondary}`,
    zIndex: 1000,
    padding: '1.25rem 0',
    boxShadow: `0 8px 32px rgba(212, 209, 204, 0.3), 0 2px 8px rgba(212, 209, 204, 0.2)`
  }}>
    <div style={{ maxWidth: '1200px', margin: '0 auto', padding: '0 1.5rem', display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
      <div style={{ display: 'flex', alignItems: 'center', gap: '0.75rem' }}>
        <SynthCard variant="premium" style={{
          padding: '0.75rem',
          borderRadius: '16px',
          minHeight: 'auto',
          background: `linear-gradient(135deg, ${synthColors.accent}, ${synthColors.accentLight})`
        }}>
          <span style={{ color: 'white', fontWeight: 'bold', fontSize: '18px', textShadow: '0 1px 2px rgba(0,0,0,0.2)' }}>S</span>
        </SynthCard>
        <div>
          <span style={{
            fontSize: '22px',
            fontWeight: 'bold',
            background: `linear-gradient(135deg, ${synthColors.text}, ${synthColors.textSecondary})`,
            WebkitBackgroundClip: 'text',
            WebkitTextFillColor: 'transparent',
            letterSpacing: '-0.5px'
          }}>
            Synth
          </span>
          <span style={{
            fontSize: '22px',
            fontWeight: '300',
            color: synthColors.textTertiary,
            marginLeft: '0.25rem'
          }}>
            Agency
          </span>
        </div>
      </div>
      <div style={{ display: 'flex', gap: '2.5rem', alignItems: 'center' }}>
        {['Home', 'Services', 'About', 'Contact'].map((item) => (
          <a key={item} href={`#${item.toLowerCase()}`} style={{
            color: synthColors.text,
            textDecoration: 'none',
            fontWeight: '500',
            fontSize: '15px',
            transition: 'all 0.3s ease',
            position: 'relative',
            padding: '0.5rem 0'
          }}>
            {item}
          </a>
        ))}
        <SynthButton variant="accent" size="md" style={{
          background: `linear-gradient(135deg, ${synthColors.accent}, ${synthColors.accentDark})`,
          boxShadow: `0 4px 12px rgba(107, 70, 193, 0.3), inset 0 1px 0 rgba(255, 255, 255, 0.2)`
        }}>
          Let's Talk
        </SynthButton>
      </div>
    </div>
  </nav>
);

// Premium Hero Component
const Hero = () => (
  <section id="home" style={{
    minHeight: '100vh',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    paddingTop: '100px',
    background: `linear-gradient(135deg, ${synthColors.bgPrimary} 0%, ${synthColors.bgSecondary} 50%, ${synthColors.bgTertiary} 100%)`,
    position: 'relative',
    overflow: 'hidden'
  }}>
    {/* Premium background effects */}
    <div style={{
      position: 'absolute',
      inset: 0,
      background: `
        radial-gradient(circle at 20% 30%, rgba(107, 70, 193, 0.08) 0%, transparent 50%),
        radial-gradient(circle at 80% 70%, rgba(217, 119, 6, 0.06) 0%, transparent 50%),
        radial-gradient(circle at 50% 50%, ${synthColors.light} 0%, transparent 70%)
      `,
      opacity: 0.8
    }} />

    {/* Floating geometric elements */}
    <div style={{
      position: 'absolute',
      top: '20%',
      left: '10%',
      width: '100px',
      height: '100px',
      background: `linear-gradient(135deg, ${synthColors.accent}20, ${synthColors.accentLight}10)`,
      borderRadius: '30px',
      transform: 'rotate(45deg)',
      filter: 'blur(1px)'
    }} />

    <div style={{
      position: 'absolute',
      bottom: '20%',
      right: '15%',
      width: '80px',
      height: '80px',
      background: `linear-gradient(135deg, ${synthColors.gold}15, ${synthColors.goldLight}08)`,
      borderRadius: '50%',
      filter: 'blur(1px)'
    }} />

    <div style={{ maxWidth: '1200px', margin: '0 auto', padding: '0 1.5rem', textAlign: 'center', position: 'relative', zIndex: 1 }}>
      {/* Premium Status Badge */}
      <SynthCard variant="premium" style={{
        display: 'inline-flex',
        alignItems: 'center',
        padding: '1rem 2rem',
        borderRadius: '60px',
        marginBottom: '3rem',
        minHeight: 'auto',
        background: `linear-gradient(135deg, ${synthColors.light}, ${synthColors.baseSecondary})`,
        border: `2px solid ${synthColors.lightSecondary}`
      }}>
        <div style={{
          width: '10px',
          height: '10px',
          background: `linear-gradient(135deg, ${synthColors.success}, #34D399)`,
          borderRadius: '50%',
          marginRight: '1rem',
          boxShadow: `0 0 12px ${synthColors.success}40, inset 0 1px 0 rgba(255, 255, 255, 0.3)`,
          animation: 'pulse 2s infinite'
        }}></div>
        <span style={{
          fontSize: '15px',
          fontWeight: '600',
          color: synthColors.text,
          letterSpacing: '0.5px'
        }}>
          Available for Premium Projects
        </span>
      </SynthCard>

      {/* Premium Main Headline */}
      <h1 style={{
        fontSize: '4.5rem',
        fontWeight: '800',
        marginBottom: '2rem',
        lineHeight: '1.1',
        color: synthColors.text,
        letterSpacing: '-2px',
        textShadow: `0 2px 4px rgba(31, 41, 55, 0.1)`
      }}>
        Crafting Premium<br />
        <span style={{
          background: `linear-gradient(135deg, ${synthColors.accent}, ${synthColors.accentLight}, ${synthColors.gold})`,
          WebkitBackgroundClip: 'text',
          WebkitTextFillColor: 'transparent',
          position: 'relative'
        }}>
          Digital Experiences
        </span><br />
        <span style={{
          fontSize: '3.5rem',
          fontWeight: '600',
          color: synthColors.textSecondary
        }}>
          That Inspire
        </span>
      </h1>

      {/* Premium Subtitle */}
      <p style={{
        fontSize: '1.4rem',
        color: synthColors.textSecondary,
        marginBottom: '3.5rem',
        maxWidth: '52rem',
        margin: '0 auto 3.5rem',
        lineHeight: '1.7',
        fontWeight: '400'
      }}>
        We're an elite design studio specializing in creating sophisticated,
        high-converting digital experiences using cutting-edge Synth design principles
        and premium aesthetic sensibilities.
      </p>

      {/* Premium CTA Buttons */}
      <div style={{ display: 'flex', gap: '2rem', justifyContent: 'center', flexWrap: 'wrap', marginBottom: '5rem' }}>
        <SynthButton variant="accent" size="lg" style={{
          background: `linear-gradient(135deg, ${synthColors.accent}, ${synthColors.accentDark})`,
          boxShadow: `0 8px 24px rgba(107, 70, 193, 0.3), inset 0 1px 0 rgba(255, 255, 255, 0.2)`
        }}>
          Start Your Project
        </SynthButton>
        <SynthButton variant="gold" size="lg">
          View Portfolio
        </SynthButton>
      </div>

      {/* Premium Stats Cards */}
      <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(220px, 1fr))', gap: '2.5rem', maxWidth: '900px', margin: '0 auto' }}>
        {[
          { number: '200+', label: 'Premium Projects', accent: synthColors.accent },
          { number: '99%', label: 'Client Satisfaction', accent: synthColors.gold },
          { number: '7+', label: 'Years of Excellence', accent: synthColors.success }
        ].map((stat, index) => (
          <SynthCard key={index} variant="premium" style={{
            textAlign: 'center',
            padding: '2rem 1.5rem',
            background: `linear-gradient(145deg, ${synthColors.light}, ${synthColors.baseSecondary})`
          }}>
            <div style={{
              fontSize: '2.5rem',
              fontWeight: '800',
              background: `linear-gradient(135deg, ${stat.accent}, ${stat.accent}CC)`,
              WebkitBackgroundClip: 'text',
              WebkitTextFillColor: 'transparent',
              marginBottom: '0.75rem',
              letterSpacing: '-1px'
            }}>
              {stat.number}
            </div>
            <div style={{
              fontSize: '1rem',
              color: synthColors.textSecondary,
              fontWeight: '600',
              letterSpacing: '0.5px'
            }}>
              {stat.label}
            </div>
          </SynthCard>
        ))}
      </div>
    </div>
  </section>
);

// Premium Services Component
const Services = () => {
  const services = [
    {
      title: 'Premium UI/UX Design',
      desc: 'Sophisticated, intuitive designs that captivate users and drive exceptional engagement using advanced Synth principles and premium aesthetics.',
      icon: '🎨',
      gradient: `linear-gradient(135deg, ${synthColors.accent}, ${synthColors.accentLight})`,
      features: ['User Research & Psychology', 'Advanced Wireframing', 'Interactive Prototyping', 'Premium Design Systems']
    },
    {
      title: 'Elite Web Development',
      desc: 'Lightning-fast, responsive websites built with cutting-edge technologies and sophisticated neumorphic interfaces.',
      icon: '💻',
      gradient: `linear-gradient(135deg, ${synthColors.gold}, ${synthColors.goldLight})`,
      features: ['React/Next.js/TypeScript', 'Node.js/GraphQL', 'Advanced Database Design', 'Premium API Integration']
    },
    {
      title: 'Strategic Consulting',
      desc: 'Data-driven strategies that align with your business goals and maximize ROI through sophisticated market analysis.',
      icon: '📊',
      gradient: `linear-gradient(135deg, ${synthColors.success}, #34D399)`,
      features: ['Deep Market Analysis', 'User Journey Optimization', 'Conversion Rate Mastery', 'Advanced Analytics']
    },
    {
      title: 'Luxury Brand Identity',
      desc: 'Cohesive, premium brand experiences that tell your story with sophistication across all touchpoints.',
      icon: '🎯',
      gradient: `linear-gradient(135deg, #EC4899, #F472B6)`,
      features: ['Premium Logo Design', 'Comprehensive Brand Guidelines', 'Luxury Visual Identity', 'High-end Marketing Materials']
    }
  ];

  return (
    <section id="services" style={{
      padding: '6rem 0',
      background: `linear-gradient(135deg, ${synthColors.bgPrimary} 0%, ${synthColors.bgSecondary} 50%, ${synthColors.bgTertiary} 100%)`,
      position: 'relative',
      overflow: 'hidden'
    }}>
      {/* Premium background elements */}
      <div style={{
        position: 'absolute',
        top: '10%',
        right: '5%',
        width: '200px',
        height: '200px',
        background: `linear-gradient(135deg, ${synthColors.accent}10, transparent)`,
        borderRadius: '50%',
        filter: 'blur(40px)'
      }} />

      <div style={{ maxWidth: '1200px', margin: '0 auto', padding: '0 1.5rem', position: 'relative', zIndex: 1 }}>
        {/* Premium Section Header */}
        <div style={{ textAlign: 'center', marginBottom: '5rem' }}>
          <h2 style={{
            fontSize: '3.5rem',
            fontWeight: '800',
            marginBottom: '2rem',
            color: synthColors.text,
            letterSpacing: '-1px'
          }}>
            Our <span style={{
              background: `linear-gradient(135deg, ${synthColors.accent}, ${synthColors.accentLight}, ${synthColors.gold})`,
              WebkitBackgroundClip: 'text',
              WebkitTextFillColor: 'transparent'
            }}>Premium Services</span>
          </h2>
          <p style={{
            fontSize: '1.3rem',
            color: synthColors.textSecondary,
            maxWidth: '52rem',
            margin: '0 auto',
            lineHeight: '1.7',
            fontWeight: '400'
          }}>
            We offer elite digital solutions using cutting-edge Synth design principles
            and premium craftsmanship to help your business dominate the digital landscape.
          </p>
        </div>

        {/* Premium Services Grid */}
        <div style={{
          display: 'grid',
          gridTemplateColumns: 'repeat(auto-fit, minmax(350px, 1fr))',
          gap: '3rem',
          marginBottom: '5rem'
        }}>
          {services.map((service, index) => (
            <SynthCard key={index} variant="premium" style={{
              height: '100%',
              background: `linear-gradient(145deg, ${synthColors.light}, ${synthColors.baseSecondary})`,
              border: `2px solid ${synthColors.lightSecondary}`
            }}>
              {/* Premium Service Icon */}
              <SynthCard variant="inset" style={{
                width: '80px',
                height: '80px',
                borderRadius: '24px',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                marginBottom: '2rem',
                padding: '0',
                background: service.gradient,
                border: 'none'
              }}>
                <span style={{
                  fontSize: '32px',
                  filter: 'drop-shadow(0 2px 4px rgba(0,0,0,0.2))'
                }}>
                  {service.icon}
                </span>
              </SynthCard>

              {/* Premium Service Content */}
              <h3 style={{
                fontSize: '1.75rem',
                fontWeight: '700',
                marginBottom: '1.25rem',
                color: synthColors.text,
                letterSpacing: '-0.5px'
              }}>
                {service.title}
              </h3>

              <p style={{
                color: synthColors.textSecondary,
                marginBottom: '2rem',
                lineHeight: '1.7',
                fontSize: '1.05rem'
              }}>
                {service.desc}
              </p>

              {/* Premium Features List */}
              <div style={{ marginBottom: '2rem' }}>
                {service.features.map((feature, featureIndex) => (
                  <div key={featureIndex} style={{
                    display: 'flex',
                    alignItems: 'center',
                    marginBottom: '0.75rem',
                    padding: '0.5rem 0'
                  }}>
                    <div style={{
                      width: '8px',
                      height: '8px',
                      background: service.gradient,
                      borderRadius: '50%',
                      marginRight: '1rem',
                      boxShadow: `0 0 8px ${synthColors.accent}40`,
                      flexShrink: 0
                    }}></div>
                    <span style={{
                      fontSize: '1rem',
                      color: synthColors.textSecondary,
                      fontWeight: '600'
                    }}>
                      {feature}
                    </span>
                  </div>
                ))}
              </div>

              {/* Premium Learn More Button */}
              <SynthButton variant="ghost" size="md" style={{
                width: '100%',
                border: `2px solid ${synthColors.darkSecondary}`,
                background: `linear-gradient(145deg, transparent, ${synthColors.base}20)`
              }}>
                Explore Service →
              </SynthButton>
            </SynthCard>
          ))}
        </div>

        {/* Premium CTA Section */}
        <SynthCard variant="premium" style={{
          textAlign: 'center',
          background: `linear-gradient(135deg, ${synthColors.light}, ${synthColors.baseSecondary})`,
          padding: '4rem 3rem',
          border: `3px solid ${synthColors.lightSecondary}`,
          position: 'relative',
          overflow: 'hidden'
        }}>
          {/* Premium background accent */}
          <div style={{
            position: 'absolute',
            top: '-50%',
            left: '-50%',
            width: '200%',
            height: '200%',
            background: `conic-gradient(from 0deg, ${synthColors.accent}05, ${synthColors.gold}05, ${synthColors.accent}05)`,
            animation: 'rotate 20s linear infinite'
          }} />

          <div style={{ position: 'relative', zIndex: 1 }}>
            <h3 style={{
              fontSize: '2.5rem',
              fontWeight: '800',
              marginBottom: '1.5rem',
              color: synthColors.text,
              letterSpacing: '-1px'
            }}>
              Ready for <span style={{
                background: `linear-gradient(135deg, ${synthColors.accent}, ${synthColors.gold})`,
                WebkitBackgroundClip: 'text',
                WebkitTextFillColor: 'transparent'
              }}>Premium Excellence</span>?
            </h3>
            <p style={{
              fontSize: '1.2rem',
              color: synthColors.textSecondary,
              marginBottom: '2.5rem',
              maxWidth: '650px',
              margin: '0 auto 2.5rem',
              lineHeight: '1.7'
            }}>
              Let's discuss your project and see how we can help bring your vision to life
              with our premium Synth-powered design approach and elite craftsmanship.
            </p>
            <div style={{ display: 'flex', gap: '1.5rem', justifyContent: 'center', flexWrap: 'wrap' }}>
              <SynthButton variant="accent" size="lg" style={{
                background: `linear-gradient(135deg, ${synthColors.accent}, ${synthColors.accentDark})`,
                boxShadow: `0 8px 24px rgba(107, 70, 193, 0.3), inset 0 1px 0 rgba(255, 255, 255, 0.2)`
              }}>
                Get Premium Quote
              </SynthButton>
              <SynthButton variant="gold" size="lg">
                View Elite Portfolio
              </SynthButton>
            </div>
          </div>
        </SynthCard>
      </div>
    </section>
  );
};

// Synth Input Component
const SynthInput = ({ type = 'text', placeholder, rows, style = {}, ...props }) => {
  const [isFocused, setIsFocused] = useState(false);

  const baseStyles = {
    width: '100%',
    padding: '1rem 1.25rem',
    borderRadius: '16px',
    border: 'none',
    background: synthColors.base,
    color: synthColors.text,
    fontSize: '16px',
    fontFamily: 'inherit',
    outline: 'none',
    transition: 'all 0.2s ease',
    boxShadow: isFocused
      ? `inset 6px 6px 12px ${synthColors.dark}, inset -6px -6px 12px ${synthColors.light}, 0 0 0 3px rgba(99, 102, 241, 0.1)`
      : `inset 4px 4px 8px ${synthColors.dark}, inset -4px -4px 8px ${synthColors.light}`
  };

  const Element = type === 'textarea' ? 'textarea' : 'input';

  return (
    <Element
      type={type !== 'textarea' ? type : undefined}
      placeholder={placeholder}
      rows={rows}
      style={{ ...baseStyles, ...style, resize: type === 'textarea' ? 'none' : undefined }}
      onFocus={() => setIsFocused(true)}
      onBlur={() => setIsFocused(false)}
      {...props}
    />
  );
};

// Contact Component with Synth styling
const Contact = () => {
  const contactInfo = [
    { icon: '📧', title: 'Email Us', content: '<EMAIL>', desc: 'Send us an email anytime' },
    { icon: '📞', title: 'Call Us', content: '+1 (555) 123-4567', desc: 'Mon-Fri from 8am to 6pm' },
    { icon: '📍', title: 'Visit Us', content: 'San Francisco, CA', desc: 'Come say hello at our office' }
  ];

  return (
    <section id="contact" style={{
      padding: '5rem 0',
      background: `linear-gradient(135deg, ${synthColors.base} 0%, ${synthColors.light} 100%)`,
      position: 'relative'
    }}>
      <div style={{ maxWidth: '1200px', margin: '0 auto', padding: '0 1.5rem' }}>
        {/* Section Header */}
        <div style={{ textAlign: 'center', marginBottom: '4rem' }}>
          <h2 style={{
            fontSize: '3rem',
            fontWeight: 'bold',
            marginBottom: '1.5rem',
            color: synthColors.text
          }}>
            Let's <span style={{
              background: `linear-gradient(135deg, ${synthColors.accent}, ${synthColors.accentDark})`,
              WebkitBackgroundClip: 'text',
              WebkitTextFillColor: 'transparent'
            }}>Work Together</span>
          </h2>
          <p style={{
            fontSize: '1.25rem',
            color: synthColors.textSecondary,
            maxWidth: '48rem',
            margin: '0 auto',
            lineHeight: '1.6'
          }}>
            Ready to start your project? Get in touch and let's create something amazing together
            using the power of Synth design.
          </p>
        </div>

        <div style={{ display: 'grid', gridTemplateColumns: '1fr 2fr', gap: '3rem', alignItems: 'start' }}>
          {/* Contact Info */}
          <div style={{ display: 'flex', flexDirection: 'column', gap: '1.5rem' }}>
            {contactInfo.map((info, index) => (
              <SynthCard key={index} variant="flat" style={{ padding: '1.5rem' }}>
                <div style={{ display: 'flex', alignItems: 'flex-start', gap: '1rem' }}>
                  <SynthCard variant="inset" style={{
                    width: '50px',
                    height: '50px',
                    borderRadius: '16px',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    padding: '0',
                    flexShrink: 0
                  }}>
                    <span style={{ fontSize: '20px' }}>{info.icon}</span>
                  </SynthCard>
                  <div>
                    <h3 style={{
                      fontSize: '1.1rem',
                      fontWeight: 'bold',
                      marginBottom: '0.25rem',
                      color: synthColors.text
                    }}>
                      {info.title}
                    </h3>
                    <p style={{
                      color: synthColors.accent,
                      fontWeight: '600',
                      marginBottom: '0.25rem',
                      fontSize: '0.95rem'
                    }}>
                      {info.content}
                    </p>
                    <p style={{
                      color: synthColors.textSecondary,
                      fontSize: '0.85rem'
                    }}>
                      {info.desc}
                    </p>
                  </div>
                </div>
              </SynthCard>
            ))}
          </div>

          {/* Contact Form */}
          <SynthCard variant="elevated" style={{ padding: '2.5rem' }}>
            <form style={{ display: 'flex', flexDirection: 'column', gap: '1.5rem' }}>
              <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '1rem' }}>
                <SynthInput type="text" placeholder="Your Name" />
                <SynthInput type="email" placeholder="Your Email" />
              </div>

              <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '1rem' }}>
                <SynthInput type="text" placeholder="Company (Optional)" />
                <SynthInput type="text" placeholder="Budget Range" />
              </div>

              <SynthInput type="text" placeholder="Project Type" />

              <SynthInput
                type="textarea"
                placeholder="Tell us about your project..."
                rows="5"
              />

              <SynthButton variant="accent" size="lg" style={{ width: '100%', marginTop: '0.5rem' }}>
                Send Message
              </SynthButton>
            </form>
          </SynthCard>
        </div>

        {/* Additional CTA */}
        <div style={{ textAlign: 'center', marginTop: '4rem' }}>
          <SynthCard variant="flat" style={{
            display: 'inline-block',
            padding: '1rem 2rem',
            background: `linear-gradient(135deg, ${synthColors.light}, ${synthColors.base})`
          }}>
            <p style={{
              color: synthColors.textSecondary,
              marginBottom: '0.5rem',
              fontSize: '0.9rem'
            }}>
              Prefer a quick chat?
            </p>
            <SynthButton variant="accent" size="sm">
              Schedule a Call
            </SynthButton>
          </SynthCard>
        </div>
      </div>
    </section>
  );
};

// Footer Component with Synth styling
const Footer = () => (
  <footer style={{
    background: `linear-gradient(135deg, ${synthColors.dark} 0%, #2D3748 100%)`,
    color: 'white',
    padding: '3rem 0 2rem',
    position: 'relative'
  }}>
    <div style={{ maxWidth: '1200px', margin: '0 auto', padding: '0 1.5rem' }}>
      {/* Main Footer Content */}
      <div style={{
        display: 'grid',
        gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))',
        gap: '2rem',
        marginBottom: '2rem'
      }}>
        {/* Brand Section */}
        <div>
          <div style={{ display: 'flex', alignItems: 'center', gap: '0.5rem', marginBottom: '1rem' }}>
            <div style={{
              width: '32px',
              height: '32px',
              background: `linear-gradient(135deg, ${synthColors.accent}, ${synthColors.accentDark})`,
              borderRadius: '8px',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              boxShadow: `0 4px 8px rgba(99, 102, 241, 0.3)`
            }}>
              <span style={{ color: 'white', fontWeight: 'bold', fontSize: '16px' }}>S</span>
            </div>
            <span style={{ fontSize: '20px', fontWeight: 'bold' }}>Synth Agency</span>
          </div>
          <p style={{ color: '#A0AEC0', lineHeight: '1.6', fontSize: '0.9rem' }}>
            Creating beautiful digital experiences with the power of Synth design system.
            Neumorphism meets modern web development.
          </p>
        </div>

        {/* Services */}
        <div>
          <h4 style={{ fontWeight: 'bold', marginBottom: '1rem', color: 'white' }}>Services</h4>
          <div style={{ display: 'flex', flexDirection: 'column', gap: '0.5rem' }}>
            {['UI/UX Design', 'Web Development', 'Digital Strategy', 'Brand Identity'].map(service => (
              <a key={service} href="#services" style={{
                color: '#A0AEC0',
                textDecoration: 'none',
                fontSize: '0.9rem',
                transition: 'color 0.2s ease'
              }}>
                {service}
              </a>
            ))}
          </div>
        </div>

        {/* Contact */}
        <div>
          <h4 style={{ fontWeight: 'bold', marginBottom: '1rem', color: 'white' }}>Get in Touch</h4>
          <div style={{ display: 'flex', flexDirection: 'column', gap: '0.5rem' }}>
            <span style={{ color: '#A0AEC0', fontSize: '0.9rem' }}>📧 <EMAIL></span>
            <span style={{ color: '#A0AEC0', fontSize: '0.9rem' }}>📞 +1 (555) 123-4567</span>
            <span style={{ color: '#A0AEC0', fontSize: '0.9rem' }}>📍 San Francisco, CA</span>
          </div>
        </div>
      </div>

      {/* Bottom Bar */}
      <div style={{
        borderTop: '1px solid #4A5568',
        paddingTop: '2rem',
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        flexWrap: 'wrap',
        gap: '1rem'
      }}>
        <p style={{ color: '#A0AEC0', fontSize: '0.9rem', margin: 0 }}>
          © 2024 Synth Agency. All rights reserved. Built with Synth Design System.
        </p>
        <div style={{ display: 'flex', gap: '1rem' }}>
          {['Privacy', 'Terms', 'Cookies'].map(link => (
            <a key={link} href="#" style={{
              color: '#A0AEC0',
              textDecoration: 'none',
              fontSize: '0.9rem',
              transition: 'color 0.2s ease'
            }}>
              {link}
            </a>
          ))}
        </div>
      </div>
    </div>
  </footer>
);

function App() {
  return (
    <>
      {/* Premium CSS Animations */}
      <style>{`
        @keyframes pulse {
          0%, 100% { opacity: 1; }
          50% { opacity: 0.7; }
        }

        @keyframes rotate {
          from { transform: rotate(0deg); }
          to { transform: rotate(360deg); }
        }

        @keyframes float {
          0%, 100% { transform: translateY(0px); }
          50% { transform: translateY(-10px); }
        }

        @keyframes shimmer {
          0% { background-position: -200% 0; }
          100% { background-position: 200% 0; }
        }

        .premium-hover:hover {
          transform: translateY(-2px) scale(1.02);
          transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .premium-text-shimmer {
          background: linear-gradient(90deg, ${synthColors.text} 25%, ${synthColors.accent} 50%, ${synthColors.text} 75%);
          background-size: 200% 100%;
          animation: shimmer 3s ease-in-out infinite;
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
        }
      `}</style>

      <div className="App" style={{
        fontFamily: 'Inter, -apple-system, BlinkMacSystemFont, "Segoe UI", sans-serif',
        background: `linear-gradient(135deg, ${synthColors.bgPrimary} 0%, ${synthColors.bgSecondary} 100%)`,
        minHeight: '100vh'
      }}>
        <Navigation />
        <main>
          <Hero />
          <Services />
          <Contact />
        </main>
        <Footer />
      </div>
    </>
  );
}

export default App;
