import React from 'react';

// Simple Navigation Component
const Navigation = () => (
  <nav style={{
    position: 'fixed',
    top: 0,
    left: 0,
    right: 0,
    background: 'rgba(248, 249, 250, 0.95)',
    backdropFilter: 'blur(20px)',
    borderBottom: '1px solid #E5E7EB',
    zIndex: 1000,
    padding: '1rem 0'
  }}>
    <div style={{ maxWidth: '1200px', margin: '0 auto', padding: '0 1.5rem', display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
      <div style={{ display: 'flex', alignItems: 'center', gap: '0.5rem' }}>
        <div style={{ width: '32px', height: '32px', background: 'linear-gradient(135deg, #6366F1, #9333EA)', borderRadius: '8px', display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
          <span style={{ color: 'white', fontWeight: 'bold', fontSize: '14px' }}>C</span>
        </div>
        <span style={{ fontSize: '20px', fontWeight: 'bold', background: 'linear-gradient(135deg, #6366F1, #4F46E5)', WebkitBackgroundClip: 'text', WebkitTextFillColor: 'transparent' }}>
          Copper Agency
        </span>
      </div>
      <div style={{ display: 'flex', gap: '2rem', alignItems: 'center' }}>
        <a href="#home" style={{ color: '#374151', textDecoration: 'none', fontWeight: '500' }}>Home</a>
        <a href="#services" style={{ color: '#374151', textDecoration: 'none', fontWeight: '500' }}>Services</a>
        <a href="#about" style={{ color: '#374151', textDecoration: 'none', fontWeight: '500' }}>About</a>
        <a href="#contact" style={{ color: '#374151', textDecoration: 'none', fontWeight: '500' }}>Contact</a>
        <button style={{
          background: 'linear-gradient(135deg, #6366F1, #4F46E5)',
          color: 'white',
          border: 'none',
          padding: '0.75rem 1.5rem',
          borderRadius: '12px',
          fontWeight: '500',
          cursor: 'pointer',
          boxShadow: '0 4px 15px rgba(99, 102, 241, 0.3)'
        }}>
          Let's Talk
        </button>
      </div>
    </div>
  </nav>
);

// Simple Hero Component
const Hero = () => (
  <section id="home" style={{ minHeight: '100vh', display: 'flex', alignItems: 'center', justifyContent: 'center', paddingTop: '80px', background: '#F8F9FA' }}>
    <div style={{ maxWidth: '1200px', margin: '0 auto', padding: '0 1.5rem', textAlign: 'center' }}>
      <div style={{ display: 'inline-flex', alignItems: 'center', padding: '0.5rem 1rem', borderRadius: '9999px', background: 'rgba(255, 255, 255, 0.8)', border: '1px solid #E5E7EB', marginBottom: '2rem' }}>
        <span style={{ width: '8px', height: '8px', background: '#10B981', borderRadius: '50%', marginRight: '0.5rem' }}></span>
        <span style={{ fontSize: '14px', fontWeight: '500', color: '#374151' }}>Available for new projects</span>
      </div>
      <h1 style={{ fontSize: '4rem', fontWeight: 'bold', marginBottom: '1.5rem', lineHeight: '1.2' }}>
        Crafting Digital<br />
        <span style={{ background: 'linear-gradient(135deg, #6366F1, #4F46E5)', WebkitBackgroundClip: 'text', WebkitTextFillColor: 'transparent' }}>
          Experiences
        </span><br />
        That Convert
      </h1>
      <p style={{ fontSize: '1.25rem', color: '#6B7280', marginBottom: '2rem', maxWidth: '48rem', margin: '0 auto 2rem' }}>
        We're a premium freelance agency specializing in creating beautiful,
        high-converting digital experiences that elevate your brand and drive results.
      </p>
      <div style={{ display: 'flex', gap: '1rem', justifyContent: 'center', flexWrap: 'wrap' }}>
        <button style={{
          background: 'linear-gradient(135deg, #6366F1, #4F46E5)',
          color: 'white',
          border: 'none',
          padding: '1rem 2rem',
          borderRadius: '12px',
          fontWeight: '500',
          fontSize: '18px',
          cursor: 'pointer',
          boxShadow: '0 4px 15px rgba(99, 102, 241, 0.3)'
        }}>
          Start Your Project
        </button>
        <button style={{
          background: 'white',
          color: '#374151',
          border: 'none',
          padding: '1rem 2rem',
          borderRadius: '12px',
          fontWeight: '500',
          fontSize: '18px',
          cursor: 'pointer',
          boxShadow: '6px 6px 12px #D1D9E6, -6px -6px 12px #FFFFFF'
        }}>
          View Our Work
        </button>
      </div>
    </div>
  </section>
);

// Simple Services Component
const Services = () => (
  <section id="services" style={{ padding: '5rem 0', background: 'white' }}>
    <div style={{ maxWidth: '1200px', margin: '0 auto', padding: '0 1.5rem' }}>
      <div style={{ textAlign: 'center', marginBottom: '4rem' }}>
        <h2 style={{ fontSize: '3rem', fontWeight: 'bold', marginBottom: '1.5rem' }}>
          Our <span style={{ background: 'linear-gradient(135deg, #6366F1, #4F46E5)', WebkitBackgroundClip: 'text', WebkitTextFillColor: 'transparent' }}>Services</span>
        </h2>
        <p style={{ fontSize: '1.25rem', color: '#6B7280', maxWidth: '48rem', margin: '0 auto' }}>
          We offer comprehensive digital solutions to help your business thrive in the digital landscape.
        </p>
      </div>
      <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))', gap: '2rem' }}>
        {[
          { title: 'UI/UX Design', desc: 'Beautiful, intuitive designs that captivate users and drive engagement.' },
          { title: 'Web Development', desc: 'Fast, responsive websites built with modern technologies.' },
          { title: 'Digital Strategy', desc: 'Data-driven strategies that align with your business goals.' },
          { title: 'Brand Identity', desc: 'Cohesive brand experiences that tell your story.' }
        ].map((service, index) => (
          <div key={index} style={{
            background: 'white',
            borderRadius: '1rem',
            padding: '2rem',
            boxShadow: '6px 6px 12px #D1D9E6, -6px -6px 12px #FFFFFF',
            transition: 'all 0.3s ease'
          }}>
            <h3 style={{ fontSize: '1.5rem', fontWeight: 'bold', marginBottom: '1rem' }}>{service.title}</h3>
            <p style={{ color: '#6B7280' }}>{service.desc}</p>
          </div>
        ))}
      </div>
    </div>
  </section>
);

// Simple Contact Component
const Contact = () => (
  <section id="contact" style={{ padding: '5rem 0', background: '#F9FAFB' }}>
    <div style={{ maxWidth: '1200px', margin: '0 auto', padding: '0 1.5rem', textAlign: 'center' }}>
      <h2 style={{ fontSize: '3rem', fontWeight: 'bold', marginBottom: '1.5rem' }}>
        Let's <span style={{ background: 'linear-gradient(135deg, #6366F1, #4F46E5)', WebkitBackgroundClip: 'text', WebkitTextFillColor: 'transparent' }}>Work Together</span>
      </h2>
      <p style={{ fontSize: '1.25rem', color: '#6B7280', marginBottom: '3rem', maxWidth: '48rem', margin: '0 auto 3rem' }}>
        Ready to start your project? Get in touch and let's create something amazing together.
      </p>
      <div style={{ maxWidth: '600px', margin: '0 auto', background: 'white', padding: '2rem', borderRadius: '1rem', boxShadow: '6px 6px 12px #D1D9E6, -6px -6px 12px #FFFFFF' }}>
        <form style={{ display: 'flex', flexDirection: 'column', gap: '1rem' }}>
          <input type="text" placeholder="Your Name" style={{ padding: '1rem', borderRadius: '12px', border: 'none', background: '#F8F9FA', boxShadow: 'inset 2px 2px 5px #D1D9E6, inset -2px -2px 5px #FFFFFF' }} />
          <input type="email" placeholder="Your Email" style={{ padding: '1rem', borderRadius: '12px', border: 'none', background: '#F8F9FA', boxShadow: 'inset 2px 2px 5px #D1D9E6, inset -2px -2px 5px #FFFFFF' }} />
          <textarea placeholder="Your Message" rows="5" style={{ padding: '1rem', borderRadius: '12px', border: 'none', background: '#F8F9FA', boxShadow: 'inset 2px 2px 5px #D1D9E6, inset -2px -2px 5px #FFFFFF', resize: 'none' }}></textarea>
          <button type="submit" style={{
            background: 'linear-gradient(135deg, #6366F1, #4F46E5)',
            color: 'white',
            border: 'none',
            padding: '1rem 2rem',
            borderRadius: '12px',
            fontWeight: '500',
            fontSize: '18px',
            cursor: 'pointer',
            boxShadow: '0 4px 15px rgba(99, 102, 241, 0.3)'
          }}>
            Send Message
          </button>
        </form>
      </div>
    </div>
  </section>
);

function App() {
  return (
    <div className="App">
      <Navigation />
      <main>
        <Hero />
        <Services />
        <Contact />
      </main>
      <footer style={{ background: '#111827', color: 'white', padding: '2rem 0', textAlign: 'center' }}>
        <p>© 2024 Copper Agency. All rights reserved.</p>
      </footer>
    </div>
  );
}

export default App;
