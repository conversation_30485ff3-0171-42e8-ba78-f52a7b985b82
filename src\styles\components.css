/* Copper Component Styles */

/* Neumorphic Base */
.copper-neumorphic {
  background: var(--copper-bg-secondary);
  box-shadow: var(--copper-shadow-outset);
  border-radius: var(--copper-radius-lg);
  transition: all var(--copper-transition-normal);
}

.copper-neumorphic:hover {
  box-shadow: var(--copper-shadow-outset-hover);
  transform: translateY(-2px);
}

.copper-neumorphic.pressed {
  box-shadow: var(--copper-shadow-pressed);
  transform: translateY(1px);
}

/* Button Styles */
.copper-button {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: var(--copper-space-3) var(--copper-space-6);
  font-size: var(--copper-text-base);
  font-weight: var(--copper-font-medium);
  border-radius: var(--copper-radius-lg);
  cursor: pointer;
  transition: all var(--copper-transition-normal);
  position: relative;
  overflow: hidden;
}

.copper-button-primary {
  background: linear-gradient(135deg, var(--copper-accent), var(--copper-accent-dark));
  color: var(--copper-text-inverse);
  box-shadow: 
    0 4px 15px rgba(99, 102, 241, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

.copper-button-primary:hover {
  transform: translateY(-2px);
  box-shadow: 
    0 6px 20px rgba(99, 102, 241, 0.4),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

.copper-button-secondary {
  background: var(--copper-bg-secondary);
  color: var(--copper-text-primary);
  box-shadow: var(--copper-shadow-outset);
}

.copper-button-secondary:hover {
  box-shadow: var(--copper-shadow-outset-hover);
  transform: translateY(-2px);
}

.copper-button-ghost {
  background: transparent;
  color: var(--copper-text-primary);
  border: 2px solid var(--copper-neutral-200);
}

.copper-button-ghost:hover {
  background: var(--copper-bg-secondary);
  border-color: var(--copper-accent);
  color: var(--copper-accent);
}

/* Card Styles */
.copper-card {
  background: var(--copper-bg-secondary);
  border-radius: var(--copper-radius-xl);
  padding: var(--copper-space-8);
  box-shadow: var(--copper-shadow-outset);
  transition: all var(--copper-transition-normal);
  position: relative;
  overflow: hidden;
}

.copper-card:hover {
  box-shadow: var(--copper-shadow-outset-hover);
  transform: translateY(-4px);
}

.copper-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.8), transparent);
}

/* Input Styles */
.copper-input {
  width: 100%;
  padding: var(--copper-space-4);
  background: var(--copper-bg-secondary);
  border-radius: var(--copper-radius-lg);
  box-shadow: var(--copper-shadow-inset-light), var(--copper-shadow-inset-dark);
  font-size: var(--copper-text-base);
  color: var(--copper-text-primary);
  transition: all var(--copper-transition-normal);
}

.copper-input:focus {
  box-shadow: 
    var(--copper-shadow-inset-light), 
    var(--copper-shadow-inset-dark),
    0 0 0 3px rgba(99, 102, 241, 0.1);
}

.copper-input::placeholder {
  color: var(--copper-text-tertiary);
}

/* Navigation Styles */
.copper-nav {
  background: rgba(248, 249, 250, 0.95);
  backdrop-filter: blur(20px);
  border-bottom: 1px solid var(--copper-neutral-200);
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: var(--copper-z-sticky);
  transition: all var(--copper-transition-normal);
}

.copper-nav.scrolled {
  background: rgba(248, 249, 250, 0.98);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

/* Icon Styles */
.copper-icon {
  width: 24px;
  height: 24px;
  display: inline-block;
  transition: all var(--copper-transition-fast);
}

.copper-icon-lg {
  width: 48px;
  height: 48px;
}

.copper-icon-xl {
  width: 64px;
  height: 64px;
}

/* Loading Animation */
.copper-loading {
  display: inline-block;
  width: 20px;
  height: 20px;
  border: 2px solid var(--copper-neutral-200);
  border-radius: 50%;
  border-top-color: var(--copper-accent);
  animation: copper-spin 1s ease-in-out infinite;
}

@keyframes copper-spin {
  to {
    transform: rotate(360deg);
  }
}

/* Gradient Text */
.copper-gradient-text {
  background: linear-gradient(135deg, var(--copper-accent), var(--copper-accent-dark));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* Utility Classes */
.flex {
  display: flex;
}

.items-center {
  align-items: center;
}

.items-start {
  align-items: flex-start;
}

.justify-center {
  justify-content: center;
}

.justify-between {
  justify-content: space-between;
}

.space-x-2 > * + * {
  margin-left: 0.5rem;
}

.space-x-3 > * + * {
  margin-left: 0.75rem;
}

.space-x-4 > * + * {
  margin-left: 1rem;
}

.space-x-6 > * + * {
  margin-left: 1.5rem;
}

.space-x-8 > * + * {
  margin-left: 2rem;
}

.space-y-3 > * + * {
  margin-top: 0.75rem;
}

.space-y-4 > * + * {
  margin-top: 1rem;
}

.space-y-6 > * + * {
  margin-top: 1.5rem;
}

.space-y-8 > * + * {
  margin-top: 2rem;
}

.gap-4 {
  gap: 1rem;
}

.gap-8 {
  gap: 2rem;
}

.gap-12 {
  gap: 3rem;
}

.gap-16 {
  gap: 4rem;
}

.grid {
  display: grid;
}

.grid-cols-1 {
  grid-template-columns: repeat(1, minmax(0, 1fr));
}

.grid-cols-2 {
  grid-template-columns: repeat(2, minmax(0, 1fr));
}

.grid-cols-3 {
  grid-template-columns: repeat(3, minmax(0, 1fr));
}

.hidden {
  display: none;
}

.block {
  display: block;
}

.w-full {
  width: 100%;
}

.h-full {
  height: 100%;
}

.max-w-md {
  max-width: 28rem;
}

.max-w-2xl {
  max-width: 42rem;
}

.max-w-3xl {
  max-width: 48rem;
}

.max-w-4xl {
  max-width: 56rem;
}

.mx-auto {
  margin-left: auto;
  margin-right: auto;
}

.mb-2 {
  margin-bottom: 0.5rem;
}

.mb-4 {
  margin-bottom: 1rem;
}

.mb-6 {
  margin-bottom: 1.5rem;
}

.mb-8 {
  margin-bottom: 2rem;
}

.mb-12 {
  margin-bottom: 3rem;
}

.mb-16 {
  margin-bottom: 4rem;
}

.mt-8 {
  margin-top: 2rem;
}

.mt-16 {
  margin-top: 4rem;
}

.ml-2 {
  margin-left: 0.5rem;
}

.mr-2 {
  margin-right: 0.5rem;
}

.mr-4 {
  margin-right: 1rem;
}

.py-4 {
  padding-top: 1rem;
  padding-bottom: 1rem;
}

.py-8 {
  padding-top: 2rem;
  padding-bottom: 2rem;
}

.py-16 {
  padding-top: 4rem;
  padding-bottom: 4rem;
}

.px-4 {
  padding-left: 1rem;
  padding-right: 1rem;
}

.px-6 {
  padding-left: 1.5rem;
  padding-right: 1.5rem;
}

.px-8 {
  padding-left: 2rem;
  padding-right: 2rem;
}

.p-2 {
  padding: 0.5rem;
}

.p-3 {
  padding: 0.75rem;
}

.p-8 {
  padding: 2rem;
}

.p-12 {
  padding: 3rem;
}

.pt-4 {
  padding-top: 1rem;
}

.pt-20 {
  padding-top: 5rem;
}

.text-center {
  text-align: center;
}

.text-left {
  text-align: left;
}

.text-sm {
  font-size: 0.875rem;
}

.text-base {
  font-size: 1rem;
}

.text-lg {
  font-size: 1.125rem;
}

.text-xl {
  font-size: 1.25rem;
}

.text-2xl {
  font-size: 1.5rem;
}

.text-3xl {
  font-size: 1.875rem;
}

.text-4xl {
  font-size: 2.25rem;
}

.text-5xl {
  font-size: 3rem;
}

.text-7xl {
  font-size: 4.5rem;
}

.font-medium {
  font-weight: 500;
}

.font-bold {
  font-weight: 700;
}

.leading-tight {
  line-height: 1.25;
}

.leading-relaxed {
  line-height: 1.625;
}

.text-gray-400 {
  color: #9CA3AF;
}

.text-gray-500 {
  color: #6B7280;
}

.text-gray-600 {
  color: #4B5563;
}

.text-gray-700 {
  color: #374151;
}

.text-gray-900 {
  color: #111827;
}

.text-white {
  color: #FFFFFF;
}

.text-indigo-600 {
  color: #4F46E5;
}

.text-indigo-400 {
  color: #818CF8;
}

.text-green-500 {
  color: #10B981;
}

.text-yellow-400 {
  color: #F59E0B;
}

.text-red-500 {
  color: #EF4444;
}

.bg-white {
  background-color: #FFFFFF;
}

.bg-gray-50 {
  background-color: #F9FAFB;
}

.bg-gray-100 {
  background-color: #F3F4F6;
}

.bg-gray-800 {
  background-color: #1F2937;
}

.bg-gray-900 {
  background-color: #111827;
}

.bg-green-500 {
  background-color: #10B981;
}

.bg-gradient-to-br {
  background-image: linear-gradient(to bottom right, var(--tw-gradient-stops));
}

.from-indigo-500 {
  --tw-gradient-from: #6366F1;
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to, rgba(99, 102, 241, 0));
}

.to-purple-600 {
  --tw-gradient-to: #9333EA;
}

.from-indigo-50 {
  --tw-gradient-from: #EEF2FF;
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to, rgba(238, 242, 255, 0));
}

.to-purple-50 {
  --tw-gradient-to: #FAF5FF;
}

.border {
  border-width: 1px;
}

.border-2 {
  border-width: 2px;
}

.border-gray-200 {
  border-color: #E5E7EB;
}

.border-gray-700 {
  border-color: #374151;
}

.border-gray-800 {
  border-color: #1F2937;
}

.border-red-500 {
  border-color: #EF4444;
}

.border-t {
  border-top-width: 1px;
}

.border-b {
  border-bottom-width: 1px;
}

.rounded-lg {
  border-radius: 0.5rem;
}

.rounded-xl {
  border-radius: 0.75rem;
}

.rounded-2xl {
  border-radius: 1rem;
}

.rounded-full {
  border-radius: 9999px;
}

.w-2 {
  width: 0.5rem;
}

.w-5 {
  width: 1.25rem;
}

.w-6 {
  width: 1.5rem;
}

.w-8 {
  width: 2rem;
}

.w-10 {
  width: 2.5rem;
}

.w-12 {
  width: 3rem;
}

.h-2 {
  height: 0.5rem;
}

.h-3 {
  height: 0.75rem;
}

.h-5 {
  height: 1.25rem;
}

.h-6 {
  height: 1.5rem;
}

.h-8 {
  height: 2rem;
}

.h-10 {
  height: 2.5rem;
}

.h-12 {
  height: 3rem;
}

.min-h-screen {
  min-height: 100vh;
}

.relative {
  position: relative;
}

.absolute {
  position: absolute;
}

.fixed {
  position: fixed;
}

.top-0 {
  top: 0;
}

.left-0 {
  left: 0;
}

.right-0 {
  right: 0;
}

.bottom-8 {
  bottom: 2rem;
}

.left-1\/2 {
  left: 50%;
}

.transform {
  transform: var(--tw-transform);
}

.-translate-x-1\/2 {
  --tw-translate-x: -50%;
}

.z-10 {
  z-index: 10;
}

.overflow-hidden {
  overflow: hidden;
}

.resize-none {
  resize: none;
}

.transition-colors {
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

.duration-200 {
  transition-duration: 200ms;
}

.hover\:text-white:hover {
  color: #FFFFFF;
}

.hover\:text-indigo-600:hover {
  color: #4F46E5;
}

.hover\:border-indigo-500:hover {
  border-color: #6366F1;
}

.focus\:outline-none:focus {
  outline: 2px solid transparent;
  outline-offset: 2px;
}

.focus\:border-indigo-500:focus {
  border-color: #6366F1;
}

.group:hover .group-hover\:translate-x-1 {
  --tw-translate-x: 0.25rem;
}

.animate-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: .5;
  }
}

.fill-current {
  fill: currentColor;
}

.flex-1 {
  flex: 1 1 0%;
}

.flex-shrink-0 {
  flex-shrink: 0;
}

.flex-col {
  flex-direction: column;
}

.italic {
  font-style: italic;
}

.backdrop-blur-sm {
  backdrop-filter: blur(4px);
}

/* Responsive Utilities */
@media (min-width: 640px) {
  .sm\:flex-row {
    flex-direction: row;
  }

  .sm\:grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }
}

@media (min-width: 768px) {
  .md\:flex {
    display: flex;
  }

  .md\:hidden {
    display: none;
  }

  .md\:block {
    display: block;
  }

  .md\:grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }

  .md\:grid-cols-3 {
    grid-template-columns: repeat(3, minmax(0, 1fr));
  }

  .md\:grid-cols-4 {
    grid-template-columns: repeat(4, minmax(0, 1fr));
  }

  .md\:text-5xl {
    font-size: 3rem;
  }

  .md\:text-7xl {
    font-size: 4.5rem;
  }

  .md\:text-2xl {
    font-size: 1.5rem;
  }

  .md\:col-span-2 {
    grid-column: span 2 / span 2;
  }

  .md\:flex-row {
    flex-direction: row;
  }

  .md\:mb-0 {
    margin-bottom: 0;
  }

  .md\:p-12 {
    padding: 3rem;
  }

  .copper-card {
    padding: var(--copper-space-6);
  }

  .copper-button {
    padding: var(--copper-space-3) var(--copper-space-5);
    font-size: var(--copper-text-sm);
  }
}

@media (min-width: 1024px) {
  .lg\:grid-cols-3 {
    grid-template-columns: repeat(3, minmax(0, 1fr));
  }

  .lg\:grid-cols-4 {
    grid-template-columns: repeat(4, minmax(0, 1fr));
  }

  .lg\:grid-cols-5 {
    grid-template-columns: repeat(5, minmax(0, 1fr));
  }

  .lg\:grid-cols-6 {
    grid-template-columns: repeat(6, minmax(0, 1fr));
  }

  .lg\:col-span-1 {
    grid-column: span 1 / span 1;
  }

  .lg\:col-span-2 {
    grid-column: span 2 / span 2;
  }
}
