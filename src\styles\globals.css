/* Global Styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html {
  scroll-behavior: smooth;
  font-size: 16px;
}

body {
  font-family: var(--copper-font-primary);
  background-color: var(--copper-bg-primary);
  color: var(--copper-text-primary);
  line-height: 1.6;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  overflow-x: hidden;
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
  font-family: var(--copper-font-display);
  font-weight: var(--copper-font-semibold);
  line-height: 1.2;
  margin-bottom: var(--copper-space-4);
}

h1 {
  font-size: var(--copper-text-5xl);
  font-weight: var(--copper-font-bold);
}

h2 {
  font-size: var(--copper-text-4xl);
}

h3 {
  font-size: var(--copper-text-3xl);
}

h4 {
  font-size: var(--copper-text-2xl);
}

h5 {
  font-size: var(--copper-text-xl);
}

h6 {
  font-size: var(--copper-text-lg);
}

p {
  margin-bottom: var(--copper-space-4);
  color: var(--copper-text-secondary);
}

/* Links */
a {
  color: var(--copper-accent);
  text-decoration: none;
  transition: color var(--copper-transition-fast);
}

a:hover {
  color: var(--copper-accent-dark);
}

/* Lists */
ul, ol {
  margin-bottom: var(--copper-space-4);
  padding-left: var(--copper-space-6);
}

li {
  margin-bottom: var(--copper-space-2);
}

/* Images */
img {
  max-width: 100%;
  height: auto;
  display: block;
}

/* Buttons */
button {
  font-family: inherit;
  cursor: pointer;
  border: none;
  outline: none;
  background: none;
  transition: all var(--copper-transition-normal);
}

button:disabled {
  cursor: not-allowed;
  opacity: 0.6;
}

/* Form Elements */
input, textarea, select {
  font-family: inherit;
  font-size: var(--copper-text-base);
  border: none;
  outline: none;
  background: transparent;
}

/* Utility Classes */
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--copper-space-6);
}

.section {
  padding: var(--copper-space-20) 0;
}

.text-center {
  text-align: center;
}

.text-left {
  text-align: left;
}

.text-right {
  text-align: right;
}

.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* Responsive Typography and Synth Components */
@media (max-width: 768px) {
  html {
    font-size: 14px;
  }

  h1 {
    font-size: 2.5rem !important;
  }

  h2 {
    font-size: 2rem !important;
  }

  h3 {
    font-size: 1.5rem !important;
  }

  .container {
    padding: 0 var(--copper-space-4);
  }

  .section {
    padding: var(--copper-space-16) 0;
  }

  /* Mobile-specific Synth adjustments */
  .synth-card-mobile {
    padding: 1.5rem !important;
    border-radius: 16px !important;
  }

  .synth-button-mobile {
    padding: 0.75rem 1.25rem !important;
    font-size: 14px !important;
  }

  .synth-grid-mobile {
    grid-template-columns: 1fr !important;
    gap: 1.5rem !important;
  }

  .synth-contact-grid {
    grid-template-columns: 1fr !important;
    gap: 2rem !important;
  }
}

/* Scrollbar Styling */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: var(--copper-neutral-100);
}

::-webkit-scrollbar-thumb {
  background: var(--copper-neutral-300);
  border-radius: var(--copper-radius-full);
}

::-webkit-scrollbar-thumb:hover {
  background: var(--copper-neutral-400);
}

/* Selection */
::selection {
  background-color: var(--copper-accent);
  color: var(--copper-text-inverse);
}

/* Focus Styles */
:focus-visible {
  outline: 2px solid var(--copper-accent);
  outline-offset: 2px;
}
