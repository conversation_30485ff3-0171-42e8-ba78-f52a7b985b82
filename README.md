# Copper Agency - Premium Freelance Website

A premium freelance agency website built with React and inspired by the Copper design system from CRED. This project showcases modern web design principles with neumorphic elements, smooth animations, and a sophisticated color palette.

## 🎨 Design Features

### Copper Design System Inspiration
- **Neumorphism**: Soft shadows and highlights creating physical, embossed effects
- **Sophisticated Color Palette**: Muted, professional colors with subtle gradients
- **Clean Typography**: Clear hierarchy with refined fonts (Inter + Playfair Display)
- **Smooth Microinteractions**: Delightful hover states and animations
- **Componentized System**: Highly modular and reusable components

### Key UI Elements
- Sticky navigation with backdrop blur
- Hero section with animated elements
- Neumorphic service cards
- Elegant contact forms with validation
- Responsive design optimized for all devices
- Loading animations and smooth transitions

## 🚀 Technologies Used

- **React 18** - Modern React with hooks
- **Vite** - Fast build tool and dev server
- **Framer Motion** - Smooth animations and transitions
- **React Intersection Observer** - Scroll-triggered animations
- **CSS Custom Properties** - Design system variables
- **Modern CSS** - Flexbox, Grid, and custom animations

## 📁 Project Structure

```
src/
├── components/
│   ├── Navigation.jsx      # Sticky navigation bar
│   ├── Hero.jsx           # Hero section with animations
│   ├── Services.jsx       # Service cards with icons
│   ├── About.jsx          # About section with values
│   ├── Testimonials.jsx   # Client testimonials
│   ├── Contact.jsx        # Contact form with validation
│   ├── Footer.jsx         # Footer with links
│   ├── Button.jsx         # Reusable button component
│   └── Card.jsx           # Neumorphic card component
├── assets/
│   └── icons.jsx          # SVG icon components
├── styles/
│   ├── globals.css        # Global styles and resets
│   ├── copper-theme.css   # Design system variables
│   └── components.css     # Component styles and utilities
└── App.jsx                # Main application component
```

## 🎯 Features

### Navigation
- Sticky header with backdrop blur
- Smooth scroll to sections
- Mobile-responsive hamburger menu
- Animated logo and menu items

### Hero Section
- Animated background elements
- Gradient text effects
- Call-to-action buttons
- Statistics showcase
- Scroll indicator

### Services
- Neumorphic service cards
- Icon integration
- Feature lists
- Hover animations
- Strategic CTA placement

### About Section
- Company story
- Core values showcase
- Achievement highlights
- Team information

### Testimonials
- Client testimonials
- Star ratings
- Company logos
- Trust indicators

### Contact Form
- Form validation
- Neumorphic input fields
- Loading states
- Error handling
- Contact information cards

## 🛠️ Installation & Setup

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd copper-freelance-agency
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Start development server**
   ```bash
   npm run dev
   ```

4. **Build for production**
   ```bash
   npm run build
   ```

## 🎨 Customization

### Colors
Edit `src/styles/copper-theme.css` to customize the color palette:
- Primary colors
- Accent colors
- Neutral shades
- Background colors

### Typography
Modify font families and sizes in the theme file:
- Font families (Inter, Playfair Display)
- Font sizes and weights
- Line heights

### Animations
Adjust animation settings in components:
- Framer Motion variants
- Transition durations
- Easing functions

## 📱 Responsive Design

The website is fully responsive with breakpoints:
- Mobile: < 768px
- Tablet: 768px - 1024px
- Desktop: > 1024px

## 🔧 Performance Optimizations

- Lazy loading with Intersection Observer
- Optimized animations with Framer Motion
- CSS custom properties for theming
- Minimal bundle size with Vite
- Efficient component structure

## 🌟 Key Design Principles

1. **Neumorphism**: Subtle shadows and highlights for depth
2. **Minimalism**: Clean layouts with generous whitespace
3. **Consistency**: Unified design system throughout
4. **Accessibility**: Focus states and semantic HTML
5. **Performance**: Smooth 60fps animations

## 📄 License

This project is licensed under the MIT License.

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Submit a pull request

## 📞 Support

For questions or support, please contact:
- Email: <EMAIL>
- Website: [Your Website URL]

---

Built with ❤️ using the Copper design system principles
